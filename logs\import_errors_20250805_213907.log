2025-08-05 21:39:34.307 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\001_吕庆_522101197203023219_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300001.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.307 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\001_吕庆_522101197203023219_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300001.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.309 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\001_吕庆_522101197203023219_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300001.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.310 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\001_吕庆_522101197203023219_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300001.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.311 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\001_吕庆_522101197203023219_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300001.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.323 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\001_吕庆_522101197203023219_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300051.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.323 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\001_吕庆_522101197203023219_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300051.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.323 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\001_吕庆_522101197203023219_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300051.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.323 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\001_吕庆_522101197203023219_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300051.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.323 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\001_吕庆_522101197203023219_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300051.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.323 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\001_吕庆_522101197203023219_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300041.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.323 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\001_吕庆_522101197203023219_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300041.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.323 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\001_吕庆_522101197203023219_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300041.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.339 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\001_吕庆_522101197203023219_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300041.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.339 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\001_吕庆_522101197203023219_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300041.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.339 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\001_吕庆_522101197203023219_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300031.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.339 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\001_吕庆_522101197203023219_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300031.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.339 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\001_吕庆_522101197203023219_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300031.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.351 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\001_吕庆_522101197203023219_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300031.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.353 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\001_吕庆_522101197203023219_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300031.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.365 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\003_韩莎莎_522501198306050822_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300003.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.365 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\003_韩莎莎_522501198306050822_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300003.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.365 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\003_韩莎莎_522501198306050822_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300003.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.370 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\003_韩莎莎_522501198306050822_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300003.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.370 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\003_韩莎莎_522501198306050822_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300003.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.380 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\003_韩莎莎_522501198306050822_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300053.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.381 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\003_韩莎莎_522501198306050822_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300053.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.382 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\003_韩莎莎_522501198306050822_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300053.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.385 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\003_韩莎莎_522501198306050822_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300053.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.386 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\003_韩莎莎_522501198306050822_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300053.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.387 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\003_韩莎莎_522501198306050822_医保\普通门诊（本地）\YB02-CXGZSSYBJ52000001-340000002025042319472300023.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.387 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\003_韩莎莎_522501198306050822_医保\普通门诊（本地）\YB02-CXGZSSYBJ52000001-340000002025042319472300023.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.387 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\003_韩莎莎_522501198306050822_医保\普通门诊（本地）\YB02-CXGZSSYBJ52000001-340000002025042319472300023.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.387 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\003_韩莎莎_522501198306050822_医保\普通门诊（本地）\YB02-CXGZSSYBJ52000001-340000002025042319472300023.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.387 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\003_韩莎莎_522501198306050822_医保\普通门诊（本地）\YB02-CXGZSSYBJ52000001-340000002025042319472300023.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.387 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\003_韩莎莎_522501198306050822_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300043.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.387 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\003_韩莎莎_522501198306050822_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300043.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.387 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\003_韩莎莎_522501198306050822_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300043.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.402 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\003_韩莎莎_522501198306050822_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300043.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.402 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\003_韩莎莎_522501198306050822_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300043.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.402 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\003_韩莎莎_522501198306050822_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300033.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.402 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\003_韩莎莎_522501198306050822_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300033.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.402 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\003_韩莎莎_522501198306050822_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300033.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.402 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\003_韩莎莎_522501198306050822_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300033.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.402 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\003_韩莎莎_522501198306050822_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300033.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.417 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\004_黄佳琴_522728197611250026_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300004.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.417 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\004_黄佳琴_522728197611250026_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300004.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.417 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\004_黄佳琴_522728197611250026_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300004.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.417 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\004_黄佳琴_522728197611250026_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300004.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.417 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\004_黄佳琴_522728197611250026_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300004.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.429 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\004_黄佳琴_522728197611250026_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300054.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.430 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\004_黄佳琴_522728197611250026_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300054.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.430 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\004_黄佳琴_522728197611250026_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300054.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.431 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\004_黄佳琴_522728197611250026_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300054.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.432 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\004_黄佳琴_522728197611250026_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300054.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.435 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\004_黄佳琴_522728197611250026_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300044.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.435 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\004_黄佳琴_522728197611250026_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300044.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.436 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\004_黄佳琴_522728197611250026_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300044.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.437 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\004_黄佳琴_522728197611250026_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300044.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.438 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\004_黄佳琴_522728197611250026_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300044.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.440 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\004_黄佳琴_522728197611250026_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300034.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.440 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\004_黄佳琴_522728197611250026_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300034.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.440 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\004_黄佳琴_522728197611250026_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300034.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.441 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\004_黄佳琴_522728197611250026_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300034.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.442 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\004_黄佳琴_522728197611250026_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300034.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.444 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\005_吕锋_522501197404160019_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300005.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.444 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\005_吕锋_522501197404160019_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300005.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.445 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\005_吕锋_522501197404160019_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300005.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.445 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\005_吕锋_522501197404160019_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300005.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.446 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\005_吕锋_522501197404160019_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300005.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.449 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\005_吕锋_522501197404160019_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300055.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.450 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\005_吕锋_522501197404160019_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300055.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.451 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\005_吕锋_522501197404160019_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300055.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.452 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\005_吕锋_522501197404160019_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300055.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.453 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\005_吕锋_522501197404160019_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300055.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.456 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\005_吕锋_522501197404160019_医保\普通门诊（本地）\YB02-CXGZSSYBJ52000001-340000002025042319472300025.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.457 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\005_吕锋_522501197404160019_医保\普通门诊（本地）\YB02-CXGZSSYBJ52000001-340000002025042319472300025.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.457 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\005_吕锋_522501197404160019_医保\普通门诊（本地）\YB02-CXGZSSYBJ52000001-340000002025042319472300025.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.459 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\005_吕锋_522501197404160019_医保\普通门诊（本地）\YB02-CXGZSSYBJ52000001-340000002025042319472300025.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.461 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\005_吕锋_522501197404160019_医保\普通门诊（本地）\YB02-CXGZSSYBJ52000001-340000002025042319472300025.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.463 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\005_吕锋_522501197404160019_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300045.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.464 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\005_吕锋_522501197404160019_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300045.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.464 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\005_吕锋_522501197404160019_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300045.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.465 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\005_吕锋_522501197404160019_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300045.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.466 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\005_吕锋_522501197404160019_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300045.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.469 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\005_吕锋_522501197404160019_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300035.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.471 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\005_吕锋_522501197404160019_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300035.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.473 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\005_吕锋_522501197404160019_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300035.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.474 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\005_吕锋_522501197404160019_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300035.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.474 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\005_吕锋_522501197404160019_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300035.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.477 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\005_吕锋_522501197404160019_医保\门诊慢特病（本地）\YB03-CXGZSSYBJ52000001-340000002025042319472300015.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.478 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\005_吕锋_522501197404160019_医保\门诊慢特病（本地）\YB03-CXGZSSYBJ52000001-340000002025042319472300015.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.479 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\005_吕锋_522501197404160019_医保\门诊慢特病（本地）\YB03-CXGZSSYBJ52000001-340000002025042319472300015.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.482 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\005_吕锋_522501197404160019_医保\门诊慢特病（本地）\YB03-CXGZSSYBJ52000001-340000002025042319472300015.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.484 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\005_吕锋_522501197404160019_医保\门诊慢特病（本地）\YB03-CXGZSSYBJ52000001-340000002025042319472300015.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.491 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\006_吕劲_522501198005130810_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300006.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.492 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\006_吕劲_522501198005130810_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300006.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.493 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\006_吕劲_522501198005130810_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300006.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.496 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\006_吕劲_522501198005130810_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300006.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.497 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\006_吕劲_522501198005130810_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300006.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.502 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\006_吕劲_522501198005130810_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300056.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.505 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\006_吕劲_522501198005130810_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300056.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.506 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\006_吕劲_522501198005130810_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300056.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.507 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\006_吕劲_522501198005130810_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300056.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.507 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\006_吕劲_522501198005130810_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300056.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.512 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\006_吕劲_522501198005130810_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300046.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.515 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\006_吕劲_522501198005130810_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300046.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.517 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\006_吕劲_522501198005130810_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300046.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.525 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\006_吕劲_522501198005130810_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300046.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.526 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\006_吕劲_522501198005130810_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300046.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.536 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\006_吕劲_522501198005130810_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300036.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.538 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\006_吕劲_522501198005130810_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300036.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.540 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\006_吕劲_522501198005130810_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300036.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.542 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\006_吕劲_522501198005130810_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300036.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.543 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\006_吕劲_522501198005130810_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300036.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.554 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\007_吕婧涵_522501200107111661_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300057.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.555 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\007_吕婧涵_522501200107111661_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300057.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.556 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\007_吕婧涵_522501200107111661_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300057.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.557 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\007_吕婧涵_522501200107111661_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300057.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.558 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\007_吕婧涵_522501200107111661_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300057.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.560 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\007_吕婧涵_522501200107111661_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300047.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.560 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\007_吕婧涵_522501200107111661_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300047.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.562 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\007_吕婧涵_522501200107111661_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300047.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.564 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\007_吕婧涵_522501200107111661_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300047.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.566 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\007_吕婧涵_522501200107111661_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300047.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.571 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\007_吕婧涵_522501200107111661_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300037.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.573 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\007_吕婧涵_522501200107111661_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300037.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.575 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\007_吕婧涵_522501200107111661_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300037.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.577 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\007_吕婧涵_522501200107111661_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300037.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.578 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\007_吕婧涵_522501200107111661_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300037.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.584 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\008_吕兴荣_52250119441001003X_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300008.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.584 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\008_吕兴荣_52250119441001003X_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300008.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.585 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\008_吕兴荣_52250119441001003X_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300008.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.592 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\008_吕兴荣_52250119441001003X_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300008.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.594 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\008_吕兴荣_52250119441001003X_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300008.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.608 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\008_吕兴荣_52250119441001003X_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300058.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.609 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\008_吕兴荣_52250119441001003X_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300058.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.610 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\008_吕兴荣_52250119441001003X_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300058.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.612 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\008_吕兴荣_52250119441001003X_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300058.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.614 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\008_吕兴荣_52250119441001003X_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300058.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.619 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\008_吕兴荣_52250119441001003X_医保\普通门诊（本地）\YB02-CXGZSSYBJ52000001-340000002025042319472300028.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.620 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\008_吕兴荣_52250119441001003X_医保\普通门诊（本地）\YB02-CXGZSSYBJ52000001-340000002025042319472300028.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.621 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\008_吕兴荣_52250119441001003X_医保\普通门诊（本地）\YB02-CXGZSSYBJ52000001-340000002025042319472300028.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.625 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\008_吕兴荣_52250119441001003X_医保\普通门诊（本地）\YB02-CXGZSSYBJ52000001-340000002025042319472300028.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.625 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\008_吕兴荣_52250119441001003X_医保\普通门诊（本地）\YB02-CXGZSSYBJ52000001-340000002025042319472300028.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.632 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\008_吕兴荣_52250119441001003X_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300048.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.634 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\008_吕兴荣_52250119441001003X_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300048.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.635 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\008_吕兴荣_52250119441001003X_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300048.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.636 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\008_吕兴荣_52250119441001003X_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300048.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.638 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\008_吕兴荣_52250119441001003X_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300048.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.645 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\008_吕兴荣_52250119441001003X_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300038.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.646 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\008_吕兴荣_52250119441001003X_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300038.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.647 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\008_吕兴荣_52250119441001003X_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300038.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.648 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\008_吕兴荣_52250119441001003X_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300038.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.649 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\008_吕兴荣_52250119441001003X_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300038.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.651 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\008_吕兴荣_52250119441001003X_医保\门诊慢特病（本地）\YB03-CXGZSSYBJ52000001-340000002025042319472300018.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.653 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\008_吕兴荣_52250119441001003X_医保\门诊慢特病（本地）\YB03-CXGZSSYBJ52000001-340000002025042319472300018.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.655 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\008_吕兴荣_52250119441001003X_医保\门诊慢特病（本地）\YB03-CXGZSSYBJ52000001-340000002025042319472300018.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.657 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\008_吕兴荣_52250119441001003X_医保\门诊慢特病（本地）\YB03-CXGZSSYBJ52000001-340000002025042319472300018.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.659 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\008_吕兴荣_52250119441001003X_医保\门诊慢特病（本地）\YB03-CXGZSSYBJ52000001-340000002025042319472300018.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.665 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\010_周丽萍_522501197507100043_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300010.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.666 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\010_周丽萍_522501197507100043_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300010.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.666 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\010_周丽萍_522501197507100043_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300010.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.667 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\010_周丽萍_522501197507100043_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300010.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.668 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\010_周丽萍_522501197507100043_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025042319472300010.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.672 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\010_周丽萍_522501197507100043_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300060.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.672 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\010_周丽萍_522501197507100043_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300060.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.674 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\010_周丽萍_522501197507100043_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300060.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.676 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\010_周丽萍_522501197507100043_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300060.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.677 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\010_周丽萍_522501197507100043_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025042319472300060.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.682 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\010_周丽萍_522501197507100043_医保\普通门诊（本地）\YB02-CXGZSSYBJ52000001-340000002025042319472300030.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.682 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\010_周丽萍_522501197507100043_医保\普通门诊（本地）\YB02-CXGZSSYBJ52000001-340000002025042319472300030.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.683 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\010_周丽萍_522501197507100043_医保\普通门诊（本地）\YB02-CXGZSSYBJ52000001-340000002025042319472300030.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.685 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\010_周丽萍_522501197507100043_医保\普通门诊（本地）\YB02-CXGZSSYBJ52000001-340000002025042319472300030.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.687 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\010_周丽萍_522501197507100043_医保\普通门诊（本地）\YB02-CXGZSSYBJ52000001-340000002025042319472300030.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.692 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\010_周丽萍_522501197507100043_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300050.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.693 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\010_周丽萍_522501197507100043_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300050.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.694 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\010_周丽萍_522501197507100043_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300050.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.695 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\010_周丽萍_522501197507100043_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300050.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.697 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\010_周丽萍_522501197507100043_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025042319472300050.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.699 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\010_周丽萍_522501197507100043_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300040.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.700 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\010_周丽萍_522501197507100043_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300040.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.701 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\010_周丽萍_522501197507100043_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300040.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.702 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\010_周丽萍_522501197507100043_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300040.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:39:34.702 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕2343号-医保\黔监查〔2025〕2343号-医保_按对象\010_周丽萍_522501197507100043_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025042319472300040.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.413 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\001_陈鹏_522527199011051156_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700090.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.417 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\001_陈鹏_522527199011051156_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700090.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.419 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\001_陈鹏_522527199011051156_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700090.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.421 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\001_陈鹏_522527199011051156_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700090.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.424 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\001_陈鹏_522527199011051156_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700090.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.427 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\001_陈鹏_522527199011051156_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025073008554700075.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.427 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\001_陈鹏_522527199011051156_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025073008554700075.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.427 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\001_陈鹏_522527199011051156_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025073008554700075.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.442 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\001_陈鹏_522527199011051156_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025073008554700075.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.442 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\001_陈鹏_522527199011051156_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025073008554700075.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.442 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\001_陈鹏_522527199011051156_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025073008554700060.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.442 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\001_陈鹏_522527199011051156_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025073008554700060.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.442 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\001_陈鹏_522527199011051156_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025073008554700060.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.442 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\001_陈鹏_522527199011051156_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025073008554700060.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.458 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\001_陈鹏_522527199011051156_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025073008554700060.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.461 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\002_姚彩_522527199204060120_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025073008554700014.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.461 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\002_姚彩_522527199204060120_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025073008554700014.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.474 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\002_姚彩_522527199204060120_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025073008554700014.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.474 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\002_姚彩_522527199204060120_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025073008554700014.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.474 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\002_姚彩_522527199204060120_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025073008554700014.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.474 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\002_姚彩_522527199204060120_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700089.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.490 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\002_姚彩_522527199204060120_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700089.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.490 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\002_姚彩_522527199204060120_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700089.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.490 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\002_姚彩_522527199204060120_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700089.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.490 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\002_姚彩_522527199204060120_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700089.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.506 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\003_黄兵_522501198309062810_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700088.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.508 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\003_黄兵_522501198309062810_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700088.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.509 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\003_黄兵_522501198309062810_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700088.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.511 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\003_黄兵_522501198309062810_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700088.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.512 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\003_黄兵_522501198309062810_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700088.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.523 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\003_黄兵_522501198309062810_医保\普通门诊（本地）\YB02-CXGZSSYBJ52000001-340000002025073008554700043.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.524 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\003_黄兵_522501198309062810_医保\普通门诊（本地）\YB02-CXGZSSYBJ52000001-340000002025073008554700043.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.524 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\003_黄兵_522501198309062810_医保\普通门诊（本地）\YB02-CXGZSSYBJ52000001-340000002025073008554700043.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.525 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\003_黄兵_522501198309062810_医保\普通门诊（本地）\YB02-CXGZSSYBJ52000001-340000002025073008554700043.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.526 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\003_黄兵_522501198309062810_医保\普通门诊（本地）\YB02-CXGZSSYBJ52000001-340000002025073008554700043.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.558 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\003_黄兵_522501198309062810_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025073008554700073.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.560 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\003_黄兵_522501198309062810_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025073008554700073.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.560 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\003_黄兵_522501198309062810_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025073008554700073.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.562 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\003_黄兵_522501198309062810_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025073008554700073.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.562 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\003_黄兵_522501198309062810_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025073008554700073.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.573 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\003_黄兵_522501198309062810_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025073008554700058.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.574 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\003_黄兵_522501198309062810_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025073008554700058.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.574 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\003_黄兵_522501198309062810_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025073008554700058.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.575 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\003_黄兵_522501198309062810_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025073008554700058.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.576 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\003_黄兵_522501198309062810_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025073008554700058.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.585 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\004_陈家倩_522527198010012345_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025073008554700012.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.593 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\004_陈家倩_522527198010012345_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025073008554700012.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.598 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\004_陈家倩_522527198010012345_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025073008554700012.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.605 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\004_陈家倩_522527198010012345_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025073008554700012.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.606 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\004_陈家倩_522527198010012345_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025073008554700012.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.611 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\004_陈家倩_522527198010012345_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700087.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.615 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\004_陈家倩_522527198010012345_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700087.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.618 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\004_陈家倩_522527198010012345_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700087.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.625 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\004_陈家倩_522527198010012345_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700087.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.628 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\004_陈家倩_522527198010012345_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700087.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.643 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\004_陈家倩_522527198010012345_医保\普通门诊（本地）\YB02-CXGZSSYBJ52000001-340000002025073008554700042.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.647 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\004_陈家倩_522527198010012345_医保\普通门诊（本地）\YB02-CXGZSSYBJ52000001-340000002025073008554700042.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.650 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\004_陈家倩_522527198010012345_医保\普通门诊（本地）\YB02-CXGZSSYBJ52000001-340000002025073008554700042.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.657 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\004_陈家倩_522527198010012345_医保\普通门诊（本地）\YB02-CXGZSSYBJ52000001-340000002025073008554700042.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.661 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\004_陈家倩_522527198010012345_医保\普通门诊（本地）\YB02-CXGZSSYBJ52000001-340000002025073008554700042.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.678 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\004_陈家倩_522527198010012345_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025073008554700072.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.681 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\004_陈家倩_522527198010012345_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025073008554700072.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.683 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\004_陈家倩_522527198010012345_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025073008554700072.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.686 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\004_陈家倩_522527198010012345_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025073008554700072.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.690 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\004_陈家倩_522527198010012345_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025073008554700072.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.704 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\004_陈家倩_522527198010012345_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025073008554700057.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.705 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\004_陈家倩_522527198010012345_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025073008554700057.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.706 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\004_陈家倩_522527198010012345_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025073008554700057.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.707 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\004_陈家倩_522527198010012345_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025073008554700057.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.707 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\004_陈家倩_522527198010012345_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025073008554700057.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.712 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\005_黄诺曦_520402200508082485_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025073008554700011.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.713 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\005_黄诺曦_520402200508082485_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025073008554700011.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.714 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\005_黄诺曦_520402200508082485_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025073008554700011.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.716 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\005_黄诺曦_520402200508082485_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025073008554700011.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.718 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\005_黄诺曦_520402200508082485_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025073008554700011.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.738 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\005_黄诺曦_520402200508082485_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700086.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.739 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\005_黄诺曦_520402200508082485_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700086.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.740 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\005_黄诺曦_520402200508082485_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700086.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.741 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\005_黄诺曦_520402200508082485_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700086.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.741 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\005_黄诺曦_520402200508082485_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700086.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.745 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\006_黄籽宁_520402200811150030_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025073008554700010.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.746 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\006_黄籽宁_520402200811150030_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025073008554700010.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.746 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\006_黄籽宁_520402200811150030_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025073008554700010.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.747 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\006_黄籽宁_520402200811150030_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025073008554700010.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.747 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\006_黄籽宁_520402200811150030_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025073008554700010.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.751 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\006_黄籽宁_520402200811150030_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700085.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.752 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\006_黄籽宁_520402200811150030_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700085.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.753 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\006_黄籽宁_520402200811150030_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700085.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.754 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\006_黄籽宁_520402200811150030_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700085.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.755 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\006_黄籽宁_520402200811150030_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700085.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.758 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\007_顾芬_52252119681228146X_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025073008554700009.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.760 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\007_顾芬_52252119681228146X_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025073008554700009.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.761 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\007_顾芬_52252119681228146X_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025073008554700009.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.763 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\007_顾芬_52252119681228146X_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025073008554700009.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.764 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\007_顾芬_52252119681228146X_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025073008554700009.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.767 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\007_顾芬_52252119681228146X_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700084.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.768 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\007_顾芬_52252119681228146X_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700084.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.769 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\007_顾芬_52252119681228146X_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700084.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.770 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\007_顾芬_52252119681228146X_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700084.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.771 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\007_顾芬_52252119681228146X_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700084.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.775 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\007_顾芬_52252119681228146X_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025073008554700069.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.776 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\007_顾芬_52252119681228146X_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025073008554700069.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.777 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\007_顾芬_52252119681228146X_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025073008554700069.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.779 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\007_顾芬_52252119681228146X_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025073008554700069.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.779 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\007_顾芬_52252119681228146X_医保\药店购药费用明细（本地）\YB05-CXGZSSYBJ52000001-340000002025073008554700069.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.783 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\007_顾芬_52252119681228146X_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025073008554700054.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.784 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\007_顾芬_52252119681228146X_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025073008554700054.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.785 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\007_顾芬_52252119681228146X_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025073008554700054.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.786 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\007_顾芬_52252119681228146X_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025073008554700054.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.787 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\007_顾芬_52252119681228146X_医保\药店购药（本地）\YB04-CXGZSSYBJ52000001-340000002025073008554700054.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.790 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\008_冯建伟_522501199011180835_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700083.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.790 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\008_冯建伟_522501199011180835_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700083.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.792 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\008_冯建伟_522501199011180835_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700083.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.793 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\008_冯建伟_522501199011180835_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700083.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.793 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\008_冯建伟_522501199011180835_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700083.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.797 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\009_项力勤_522526199108300028_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025073008554700007.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.798 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\009_项力勤_522526199108300028_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025073008554700007.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.799 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\009_项力勤_522526199108300028_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025073008554700007.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.801 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\009_项力勤_522526199108300028_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025073008554700007.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.802 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\009_项力勤_522526199108300028_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025073008554700007.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.808 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\009_项力勤_522526199108300028_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700082.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.808 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\009_项力勤_522526199108300028_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700082.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.808 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\009_项力勤_522526199108300028_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700082.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.810 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\009_项力勤_522526199108300028_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700082.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.810 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\009_项力勤_522526199108300028_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700082.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.815 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\011_刘小飞_520113197607150437_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700080.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.815 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\011_刘小飞_520113197607150437_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700080.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.816 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\011_刘小飞_520113197607150437_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700080.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.817 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\011_刘小飞_520113197607150437_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700080.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.818 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\011_刘小飞_520113197607150437_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700080.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.821 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\012_陈仁英_522224198810061641_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025073008554700004.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.821 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\012_陈仁英_522224198810061641_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025073008554700004.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.822 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\012_陈仁英_522224198810061641_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025073008554700004.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.823 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\012_陈仁英_522224198810061641_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025073008554700004.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.824 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\012_陈仁英_522224198810061641_医保\住院结算数据（本地）\YB01-CXGZSSYBJ52000001-340000002025073008554700004.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.827 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\012_陈仁英_522224198810061641_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700079.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.828 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\012_陈仁英_522224198810061641_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700079.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.829 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\012_陈仁英_522224198810061641_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700079.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.830 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\012_陈仁英_522224198810061641_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700079.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.830 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\012_陈仁英_522224198810061641_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700079.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.838 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\012_陈仁英_522224198810061641_医保\普通门诊（本地）\YB02-CXGZSSYBJ52000001-340000002025073008554700034.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.840 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\012_陈仁英_522224198810061641_医保\普通门诊（本地）\YB02-CXGZSSYBJ52000001-340000002025073008554700034.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.841 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\012_陈仁英_522224198810061641_医保\普通门诊（本地）\YB02-CXGZSSYBJ52000001-340000002025073008554700034.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.842 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\012_陈仁英_522224198810061641_医保\普通门诊（本地）\YB02-CXGZSSYBJ52000001-340000002025073008554700034.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.842 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\012_陈仁英_522224198810061641_医保\普通门诊（本地）\YB02-CXGZSSYBJ52000001-340000002025073008554700034.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.851 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\013_杨伶_522221197901214611_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700078.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.851 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\013_杨伶_522221197901214611_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700078.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.852 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\013_杨伶_522221197901214611_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700078.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.853 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\013_杨伶_522221197901214611_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700078.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.853 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\013_杨伶_522221197901214611_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700078.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.857 - ERROR - [MainThread:18180] - import_data.py:8415 - get_all_worksheets_before_filter() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\014_吕栎宇_520103200707126719_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700077.xls 全部工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.858 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\014_吕栎宇_520103200707126719_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700077.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.858 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\014_吕栎宇_520103200707126719_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700077.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.859 - ERROR - [MainThread:18180] - import_data.py:8521 - get_file_worksheets_fast() - 快速获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\014_吕栎宇_520103200707126719_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700077.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
2025-08-05 21:41:08.860 - ERROR - [MainThread:18180] - import_data.py:8368 - get_file_worksheets() - 获取文件 G:/SJW/2025线索/吕庆/吕庆2025.8.5央地\黔监查〔2025〕4899号-医保\黔监查〔2025〕4899号-医保_按对象\014_吕栎宇_520103200707126719_医保\参保信息（本地）\YB06-CXGZSSYBJ52000001-340000002025073008554700077.xls 工作表信息时出错: Pandas requires version '2.0.1' or newer of 'xlrd' (version '1.2.0' currently installed).
